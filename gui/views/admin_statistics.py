"""
Admin statistics view with charts and graphs for attendance and quiz data.
"""
import flet as ft
from gui.components.admin_layout import create_admin_page_layout
from gui.services.statistics_service import get_attendance_statistics, get_quiz_statistics, get_filter_options
from gui.components.charts import (
    create_attendance_line_chart, create_attendance_bar_chart, create_attendance_pie_chart,
    create_quiz_score_line_chart, create_quiz_performance_bar_chart, create_quiz_score_distribution_pie_chart,
    create_chart_container, create_stats_summary_card
)
from gui.config.constants import ROUTE_LOGIN


def create_admin_statistics_view(page: ft.Page):
    """Create the admin statistics view with charts and filters."""
    current_user = getattr(page.app_state, 'current_user', None)
    if not current_user or current_user.get('role') != 'admin':
        page.go(ROUTE_LOGIN)
        return ft.View(route="/admin/statistics", controls=[])

    # State variables for filters
    selected_days = ft.Ref[ft.Dropdown]()
    selected_class = ft.Ref[ft.Dropdown]()
    selected_subject = ft.Ref[ft.Dropdown]()
    
    # Content containers
    attendance_charts_container = ft.Ref[ft.Column]()
    quiz_charts_container = ft.Ref[ft.Column]()
    summary_cards_container = ft.Ref[ft.Row]()

    def update_charts(_=None):
        """Update all charts based on current filter selections."""
        # Get filter values
        days = int(selected_days.current.value) if selected_days.current.value else 30
        class_id = int(selected_class.current.value) if selected_class.current.value and selected_class.current.value != "all" else None
        subject_id = int(selected_subject.current.value) if selected_subject.current.value and selected_subject.current.value != "all" else None
        
        # Get statistics data
        attendance_stats = get_attendance_statistics(days=days, class_id=class_id, subject_id=subject_id)
        quiz_stats = get_quiz_statistics(days=days, class_id=class_id, subject_id=subject_id)
        
        # Update attendance charts
        attendance_charts = [
            create_chart_container(
                "Évolution du Taux de Présence",
                create_attendance_line_chart(attendance_stats['daily_stats']),
                f"Période: {attendance_stats['date_range']['start']} - {attendance_stats['date_range']['end']}"
            ),
            create_chart_container(
                "Présence par Classe",
                create_attendance_bar_chart(attendance_stats['class_stats'])
            ),
            create_chart_container(
                "Répartition Présence/Absence",
                create_attendance_pie_chart(attendance_stats['overall_stats'])
            ),
        ]
        
        # Update quiz charts
        quiz_charts = [
            create_chart_container(
                "Évolution des Scores de Quiz",
                create_quiz_score_line_chart(quiz_stats['daily_stats']),
                f"Période: {quiz_stats['date_range']['start']} - {quiz_stats['date_range']['end']}"
            ),
            create_chart_container(
                "Performance par Classe",
                create_quiz_performance_bar_chart(quiz_stats['class_stats'])
            ),
            create_chart_container(
                "Distribution des Scores",
                create_quiz_score_distribution_pie_chart(quiz_stats['score_distribution'])
            ),
        ]
        
        # Update summary cards
        attendance_overall = attendance_stats['overall_stats']
        quiz_overall = quiz_stats['overall_stats']
        
        summary_cards = [
            create_stats_summary_card(
                "Taux de Présence",
                f"{attendance_overall.get('overall_rate', 0)}%",
                f"{attendance_overall.get('total_present', 0)} présents",
                ft.Icons.CHECK_CIRCLE,
                ft.Colors.GREEN_600
            ),
            create_stats_summary_card(
                "Total Absences",
                attendance_overall.get('total_absent', 0),
                "étudiants absents",
                ft.Icons.CANCEL,
                ft.Colors.RED_600
            ),
            create_stats_summary_card(
                "Score Moyen Quiz",
                f"{quiz_overall.get('overall_avg_score', 0)}%",
                f"{quiz_overall.get('total_submissions', 0)} soumissions",
                ft.Icons.QUIZ,
                ft.Colors.PURPLE_600
            ),
            create_stats_summary_card(
                "Quiz Actifs",
                quiz_overall.get('total_quizzes', 0),
                f"{quiz_overall.get('total_students', 0)} étudiants",
                ft.Icons.ASSIGNMENT,
                ft.Colors.BLUE_600
            ),
        ]
        
        # Update containers
        attendance_charts_container.current.controls = attendance_charts
        quiz_charts_container.current.controls = quiz_charts
        summary_cards_container.current.controls = summary_cards
        
        page.update()

    # Get filter options
    filter_options = get_filter_options()
    
    # Create filter controls
    selected_days.current = ft.Dropdown(
        label="Période",
        value="30",
        options=[
            ft.dropdown.Option("7", "7 jours"),
            ft.dropdown.Option("30", "30 jours"),
            ft.dropdown.Option("90", "90 jours"),
            ft.dropdown.Option("365", "1 an"),
        ],
        width=150,
        on_change=update_charts
    )
    
    class_options = [ft.dropdown.Option("all", "Toutes les classes")]
    class_options.extend([
        ft.dropdown.Option(str(cls['id']), cls['name']) 
        for cls in filter_options['classes']
    ])
    
    selected_class.current = ft.Dropdown(
        label="Classe",
        value="all",
        options=class_options,
        width=200,
        on_change=update_charts
    )
    
    subject_options = [ft.dropdown.Option("all", "Toutes les matières")]
    subject_options.extend([
        ft.dropdown.Option(str(subj['id']), subj['name']) 
        for subj in filter_options['subjects']
    ])
    
    selected_subject.current = ft.Dropdown(
        label="Matière",
        value="all",
        options=subject_options,
        width=200,
        on_change=update_charts
    )
    
    # Create filter section
    filter_section = ft.Container(
        content=ft.Column([
            ft.Text(
                "Filtres",
                size=18,
                weight=ft.FontWeight.BOLD,
            ),
            ft.Row([
                selected_days.current,
                selected_class.current,
                selected_subject.current,
                ft.ElevatedButton(
                    "Actualiser",
                    icon=ft.Icons.REFRESH,
                    on_click=update_charts,
                    style=ft.ButtonStyle(
                        bgcolor=ft.Colors.BLUE_600,
                        color=ft.Colors.WHITE,
                    )
                )
            ], spacing=20, wrap=True)
        ], spacing=10),
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(12),
        padding=ft.padding.all(20),
        margin=ft.margin.only(bottom=20),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=10,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
            offset=ft.Offset(0, 4)
        ),
    )
    
    # Create summary cards container
    summary_cards_container.current = ft.Row(
        spacing=20,
        wrap=True,
        alignment=ft.MainAxisAlignment.CENTER,
    )
    
    # Create chart containers
    attendance_charts_container.current = ft.Column(spacing=20)
    quiz_charts_container.current = ft.Column(spacing=20)
    
    # Create tabs for different chart categories
    tabs = ft.Tabs(
        selected_index=0,
        animation_duration=300,
        tabs=[
            ft.Tab(
                text="Présences",
                icon=ft.Icons.CHECK_CIRCLE,
                content=ft.Container(
                    content=attendance_charts_container.current,
                    padding=ft.padding.all(20),
                )
            ),
            ft.Tab(
                text="Quiz",
                icon=ft.Icons.QUIZ,
                content=ft.Container(
                    content=quiz_charts_container.current,
                    padding=ft.padding.all(20),
                )
            ),
        ],
        expand=True,
    )
    
    # Main content
    content = [
        filter_section,
        ft.Container(
            content=summary_cards_container.current,
            alignment=ft.alignment.center,
            margin=ft.margin.only(bottom=20),
        ),
        tabs,
    ]
    
    # Initialize charts with default data
    update_charts()
    
    return create_admin_page_layout(
        page,
        "Statistiques",
        content
    )
