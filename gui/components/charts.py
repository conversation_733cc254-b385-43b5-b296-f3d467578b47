"""
Chart components for displaying statistics in the admin interface.
"""
import flet as ft
from datetime import datetime


def create_attendance_line_chart(daily_stats):
    """
    Create a line chart for daily attendance rates.
    
    Args:
        daily_stats: List of daily attendance statistics
        
    Returns:
        ft.LineChart: Line chart component
    """
    if not daily_stats:
        return ft.Container(
            content=ft.Text("Aucune donnée disponible", size=16, text_align=ft.TextAlign.CENTER),
            alignment=ft.alignment.center,
            height=300
        )
    
    # Prepare data points
    data_points = []
    for i, stat in enumerate(daily_stats):
        data_points.append(
            ft.LineChartDataPoint(
                x=i,
                y=stat.get('attendance_rate', 0)
            )
        )
    
    # Create chart
    chart = ft.LineChart(
        data_series=[
            ft.LineChartData(
                data_points=data_points,
                stroke_width=3,
                color=ft.Colors.BLUE_600,
                curved=True,
                stroke_cap_round=True,
            )
        ],
        border=ft.Border(
            bottom=ft.BorderSide(2, ft.Colors.with_opacity(0.8, ft.Colors.GREY)),
            left=ft.BorderSide(2, ft.Colors.with_opacity(0.8, ft.Colors.GREY)),
        ),
        horizontal_grid_lines=ft.ChartGridLines(
            color=ft.Colors.with_opacity(0.2, ft.Colors.GREY),
            width=1,
        ),
        vertical_grid_lines=ft.ChartGridLines(
            color=ft.Colors.with_opacity(0.2, ft.Colors.GREY),
            width=1,
        ),
        left_axis=ft.ChartAxis(
            title=ft.Text("Taux de présence (%)", size=12),
            title_size=12,
        ),
        bottom_axis=ft.ChartAxis(
            title=ft.Text("Jours", size=12),
            title_size=12,
        ),
        min_y=0,
        max_y=100,
        expand=True,
    )
    
    return ft.Container(
        content=chart,
        height=300,
        padding=ft.padding.all(10),
    )


def create_attendance_bar_chart(class_stats):
    """
    Create a bar chart for attendance by class.
    
    Args:
        class_stats: List of class attendance statistics
        
    Returns:
        ft.BarChart: Bar chart component
    """
    if not class_stats:
        return ft.Container(
            content=ft.Text("Aucune donnée disponible", size=16, text_align=ft.TextAlign.CENTER),
            alignment=ft.alignment.center,
            height=300
        )
    
    # Prepare bar groups
    bar_groups = []
    for i, stat in enumerate(class_stats[:10]):  # Limit to 10 classes for readability
        bar_groups.append(
            ft.BarChartGroup(
                x=i,
                bar_rods=[
                    ft.BarChartRod(
                        from_y=0,
                        to_y=stat.get('attendance_rate', 0),
                        width=20,
                        color=ft.Colors.GREEN_600,
                        tooltip=f"{stat.get('class_name', 'N/A')}: {stat.get('attendance_rate', 0)}%",
                        border_radius=ft.border_radius.only(top_left=3, top_right=3),
                    )
                ],
            )
        )
    
    chart = ft.BarChart(
        bar_groups=bar_groups,
        border=ft.Border(
            bottom=ft.BorderSide(2, ft.Colors.with_opacity(0.8, ft.Colors.GREY)),
            left=ft.BorderSide(2, ft.Colors.with_opacity(0.8, ft.Colors.GREY)),
        ),
        horizontal_grid_lines=ft.ChartGridLines(
            color=ft.Colors.with_opacity(0.2, ft.Colors.GREY),
            width=1,
        ),
        left_axis=ft.ChartAxis(
            title=ft.Text("Taux de présence (%)", size=12),
            title_size=12,
        ),
        bottom_axis=ft.ChartAxis(
            title=ft.Text("Classes", size=12),
            title_size=12,
        ),
        max_y=100,
        expand=True,
    )
    
    return ft.Container(
        content=chart,
        height=300,
        padding=ft.padding.all(10),
    )


def create_attendance_pie_chart(overall_stats):
    """
    Create a pie chart for overall attendance distribution.
    
    Args:
        overall_stats: Overall attendance statistics
        
    Returns:
        ft.PieChart: Pie chart component
    """
    total_present = overall_stats.get('total_present', 0)
    total_absent = overall_stats.get('total_absent', 0)
    
    if total_present == 0 and total_absent == 0:
        return ft.Container(
            content=ft.Text("Aucune donnée disponible", size=16, text_align=ft.TextAlign.CENTER),
            alignment=ft.alignment.center,
            height=300
        )
    
    sections = []
    if total_present > 0:
        sections.append(
            ft.PieChartSection(
                value=total_present,
                title=f"Présent\n{total_present}",
                title_style=ft.TextStyle(
                    size=12,
                    color=ft.Colors.WHITE,
                    weight=ft.FontWeight.BOLD,
                ),
                color=ft.Colors.GREEN_600,
                radius=100,
            )
        )
    
    if total_absent > 0:
        sections.append(
            ft.PieChartSection(
                value=total_absent,
                title=f"Absent\n{total_absent}",
                title_style=ft.TextStyle(
                    size=12,
                    color=ft.Colors.WHITE,
                    weight=ft.FontWeight.BOLD,
                ),
                color=ft.Colors.RED_600,
                radius=100,
            )
        )
    
    chart = ft.PieChart(
        sections=sections,
        sections_space=2,
        center_space_radius=40,
        expand=True,
    )
    
    return ft.Container(
        content=chart,
        height=300,
        padding=ft.padding.all(10),
    )


def create_chart_container(title, chart_widget, subtitle=None):
    """
    Create a container for charts with title and styling.
    
    Args:
        title: Chart title
        chart_widget: The chart widget
        subtitle: Optional subtitle
        
    Returns:
        ft.Container: Styled chart container
    """
    content = [
        ft.Text(
            title,
            size=18,
            weight=ft.FontWeight.BOLD,
            text_align=ft.TextAlign.CENTER,
        )
    ]
    
    if subtitle:
        content.append(
            ft.Text(
                subtitle,
                size=12,
                color=ft.Colors.GREY_600,
                text_align=ft.TextAlign.CENTER,
            )
        )
    
    content.append(chart_widget)
    
    return ft.Container(
        content=ft.Column(
            content,
            alignment=ft.MainAxisAlignment.START,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=10,
        ),
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(12),
        padding=ft.padding.all(20),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=10,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
            offset=ft.Offset(0, 4)
        ),
        margin=ft.margin.all(10),
    )


def create_stats_summary_card(title, value, subtitle=None, icon=None, color=None):
    """
    Create a summary statistics card.
    
    Args:
        title: Card title
        value: Main value to display
        subtitle: Optional subtitle
        icon: Optional icon
        color: Optional color theme
        
    Returns:
        ft.Container: Statistics card
    """
    color = color or ft.Colors.BLUE_600
    
    content = []
    
    if icon:
        content.append(
            ft.Icon(icon, size=32, color=color)
        )
    
    content.extend([
        ft.Text(
            str(value),
            size=28,
            weight=ft.FontWeight.BOLD,
            color=color,
            text_align=ft.TextAlign.CENTER,
        ),
        ft.Text(
            title,
            size=14,
            weight=ft.FontWeight.W_500,
            text_align=ft.TextAlign.CENTER,
        )
    ])
    
    if subtitle:
        content.append(
            ft.Text(
                subtitle,
                size=12,
                color=ft.Colors.GREY_600,
                text_align=ft.TextAlign.CENTER,
            )
        )
    
    return ft.Container(
        content=ft.Column(
            content,
            alignment=ft.MainAxisAlignment.CENTER,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=8,
        ),
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(12),
        padding=ft.padding.all(20),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=10,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
            offset=ft.Offset(0, 4)
        ),
        width=200,
        height=150,
    )


def create_quiz_score_line_chart(daily_stats):
    """
    Create a line chart for daily quiz scores.

    Args:
        daily_stats: List of daily quiz statistics

    Returns:
        ft.LineChart: Line chart component
    """
    if not daily_stats:
        return ft.Container(
            content=ft.Text("Aucune donnée disponible", size=16, text_align=ft.TextAlign.CENTER),
            alignment=ft.alignment.center,
            height=300
        )

    # Prepare data points
    avg_data_points = []
    max_data_points = []
    min_data_points = []

    for i, stat in enumerate(daily_stats):
        avg_data_points.append(
            ft.LineChartDataPoint(x=i, y=stat.get('avg_score', 0))
        )
        max_data_points.append(
            ft.LineChartDataPoint(x=i, y=stat.get('max_score', 0))
        )
        min_data_points.append(
            ft.LineChartDataPoint(x=i, y=stat.get('min_score', 0))
        )

    # Create chart
    chart = ft.LineChart(
        data_series=[
            ft.LineChartData(
                data_points=avg_data_points,
                stroke_width=3,
                color=ft.Colors.BLUE_600,
                curved=True,
                stroke_cap_round=True,
            ),
            ft.LineChartData(
                data_points=max_data_points,
                stroke_width=2,
                color=ft.Colors.GREEN_600,
                curved=True,
                stroke_cap_round=True,
            ),
            ft.LineChartData(
                data_points=min_data_points,
                stroke_width=2,
                color=ft.Colors.RED_600,
                curved=True,
                stroke_cap_round=True,
            ),
        ],
        border=ft.Border(
            bottom=ft.BorderSide(2, ft.Colors.with_opacity(0.8, ft.Colors.GREY)),
            left=ft.BorderSide(2, ft.Colors.with_opacity(0.8, ft.Colors.GREY)),
        ),
        horizontal_grid_lines=ft.ChartGridLines(
            color=ft.Colors.with_opacity(0.2, ft.Colors.GREY),
            width=1,
        ),
        vertical_grid_lines=ft.ChartGridLines(
            color=ft.Colors.with_opacity(0.2, ft.Colors.GREY),
            width=1,
        ),
        left_axis=ft.ChartAxis(
            title=ft.Text("Score (%)", size=12),
            title_size=12,
        ),
        bottom_axis=ft.ChartAxis(
            title=ft.Text("Jours", size=12),
            title_size=12,
        ),
        min_y=0,
        max_y=100,
        expand=True,
    )

    return ft.Container(
        content=chart,
        height=300,
        padding=ft.padding.all(10),
    )


def create_quiz_performance_bar_chart(class_stats):
    """
    Create a bar chart for quiz performance by class.

    Args:
        class_stats: List of class quiz statistics

    Returns:
        ft.BarChart: Bar chart component
    """
    if not class_stats:
        return ft.Container(
            content=ft.Text("Aucune donnée disponible", size=16, text_align=ft.TextAlign.CENTER),
            alignment=ft.alignment.center,
            height=300
        )

    # Prepare bar groups
    bar_groups = []
    for i, stat in enumerate(class_stats[:10]):  # Limit to 10 classes for readability
        bar_groups.append(
            ft.BarChartGroup(
                x=i,
                bar_rods=[
                    ft.BarChartRod(
                        from_y=0,
                        to_y=stat.get('avg_score', 0),
                        width=20,
                        color=ft.Colors.PURPLE_600,
                        tooltip=f"{stat.get('class_name', 'N/A')}: {stat.get('avg_score', 0)}%",
                        border_radius=ft.border_radius.only(top_left=3, top_right=3),
                    )
                ],
            )
        )

    chart = ft.BarChart(
        bar_groups=bar_groups,
        border=ft.Border(
            bottom=ft.BorderSide(2, ft.Colors.with_opacity(0.8, ft.Colors.GREY)),
            left=ft.BorderSide(2, ft.Colors.with_opacity(0.8, ft.Colors.GREY)),
        ),
        horizontal_grid_lines=ft.ChartGridLines(
            color=ft.Colors.with_opacity(0.2, ft.Colors.GREY),
            width=1,
        ),
        left_axis=ft.ChartAxis(
            title=ft.Text("Score moyen (%)", size=12),
            title_size=12,
        ),
        bottom_axis=ft.ChartAxis(
            title=ft.Text("Classes", size=12),
            title_size=12,
        ),
        max_y=100,
        expand=True,
    )

    return ft.Container(
        content=chart,
        height=300,
        padding=ft.padding.all(10),
    )


def create_quiz_score_distribution_pie_chart(score_distribution):
    """
    Create a pie chart for quiz score distribution.

    Args:
        score_distribution: List of score distribution data

    Returns:
        ft.PieChart: Pie chart component
    """
    if not score_distribution:
        return ft.Container(
            content=ft.Text("Aucune donnée disponible", size=16, text_align=ft.TextAlign.CENTER),
            alignment=ft.alignment.center,
            height=300
        )

    # Color mapping for score ranges
    colors = {
        '90-100%': ft.Colors.GREEN_600,
        '80-89%': ft.Colors.LIGHT_GREEN_600,
        '70-79%': ft.Colors.YELLOW_600,
        '60-69%': ft.Colors.ORANGE_600,
        'Below 60%': ft.Colors.RED_600,
    }

    sections = []
    for dist in score_distribution:
        score_range = dist.get('score_range', '')
        count = dist.get('count', 0)

        if count > 0:
            sections.append(
                ft.PieChartSection(
                    value=count,
                    title=f"{score_range}\n{count}",
                    title_style=ft.TextStyle(
                        size=10,
                        color=ft.Colors.WHITE,
                        weight=ft.FontWeight.BOLD,
                    ),
                    color=colors.get(score_range, ft.Colors.GREY_600),
                    radius=100,
                )
            )

    chart = ft.PieChart(
        sections=sections,
        sections_space=2,
        center_space_radius=40,
        expand=True,
    )

    return ft.Container(
        content=chart,
        height=300,
        padding=ft.padding.all(10),
    )
