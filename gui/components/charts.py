"""
Chart components for displaying statistics in the admin interface.
"""
import flet as ft
from datetime import datetime


def create_attendance_line_chart(daily_stats):
    """
    Create a line chart for daily attendance rates.
    
    Args:
        daily_stats: List of daily attendance statistics
        
    Returns:
        ft.LineChart: Line chart component
    """
    if not daily_stats:
        return ft.Container(
            content=ft.Text("Aucune donnée disponible", size=16, text_align=ft.TextAlign.CENTER),
            alignment=ft.alignment.center,
            height=300
        )
    
    # Prepare data points with proper x-axis values
    data_points = []
    x_labels = []
    for i, stat in enumerate(daily_stats):
        attendance_rate = stat.get('attendance_rate', 0)
        present_count = stat.get('present_count', 0)
        total_records = stat.get('total_records', 0)

        data_points.append(
            ft.LineChartDataPoint(
                x=i,
                y=attendance_rate,
                tooltip=f"📅 {stat.get('date', 'N/A')}\n📊 Taux: {attendance_rate}%\n✅ Présents: {present_count}\n📝 Total: {total_records}"
            )
        )
        # Format date for x-axis labels
        date_str = stat.get('date', '')
        if date_str:
            try:
                from datetime import datetime
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                x_labels.append(date_obj.strftime('%d/%m'))
            except:
                x_labels.append(date_str[-5:])  # Last 5 chars (MM-DD)
        else:
            x_labels.append(f"J{i+1}")
    
    # Create chart
    chart = ft.LineChart(
        data_series=[
            ft.LineChartData(
                data_points=data_points,
                stroke_width=3,
                color=ft.Colors.BLUE_600,
                curved=True,
                stroke_cap_round=True,
            )
        ],
        border=ft.Border(
            bottom=ft.BorderSide(2, ft.Colors.with_opacity(0.8, ft.Colors.GREY)),
            left=ft.BorderSide(2, ft.Colors.with_opacity(0.8, ft.Colors.GREY)),
        ),
        horizontal_grid_lines=ft.ChartGridLines(
            color=ft.Colors.with_opacity(0.2, ft.Colors.GREY),
            width=1,
        ),
        vertical_grid_lines=ft.ChartGridLines(
            color=ft.Colors.with_opacity(0.2, ft.Colors.GREY),
            width=1,
        ),
        left_axis=ft.ChartAxis(
            title=ft.Text("Taux de présence (%)", size=12),
            title_size=12,
            labels_size=10,
            labels_interval=20,  # Show labels every 20%
        ),
        bottom_axis=ft.ChartAxis(
            title=ft.Text("Dates", size=12),
            title_size=12,
            labels_size=10,
        ),
        min_y=0,
        max_y=100,
        expand=True,
    )

    return ft.Container(
        content=chart,
        height=400,
        padding=ft.padding.all(40),
    )


def create_attendance_bar_chart(class_stats):
    """
    Create a bar chart for attendance by class.
    
    Args:
        class_stats: List of class attendance statistics
        
    Returns:
        ft.BarChart: Bar chart component
    """
    if not class_stats:
        return ft.Container(
            content=ft.Text("Aucune donnée disponible", size=16, text_align=ft.TextAlign.CENTER),
            alignment=ft.alignment.center,
            height=300
        )
    
    # Prepare bar groups with class names
    bar_groups = []
    x_labels = []
    for i, stat in enumerate(class_stats[:8]):  # Limit to 8 classes for better readability
        class_name = stat.get('class_name', f'Classe {i+1}')
        x_labels.append(class_name[:8])  # Truncate long names

        bar_groups.append(
            ft.BarChartGroup(
                x=i,
                bar_rods=[
                    ft.BarChartRod(
                        from_y=0,
                        to_y=stat.get('attendance_rate', 0),
                        width=25,
                        color=ft.Colors.GREEN_600,
                        tooltip=f"{class_name}: {stat.get('attendance_rate', 0)}%\nPrésents: {stat.get('present_count', 0)}/{stat.get('total_records', 0)}",
                        border_radius=ft.border_radius.only(top_left=4, top_right=4),
                        tooltip_style=ft.TextStyle(
                            color=ft.Colors.WHITE,
                            size=12,
                            weight=ft.FontWeight.BOLD,
                        ),
                    )
                ],
            )
        )
    
    chart = ft.BarChart(
        bar_groups=bar_groups,
        border=ft.Border(
            bottom=ft.BorderSide(2, ft.Colors.with_opacity(0.8, ft.Colors.GREY)),
            left=ft.BorderSide(2, ft.Colors.with_opacity(0.8, ft.Colors.GREY)),
        ),
        horizontal_grid_lines=ft.ChartGridLines(
            color=ft.Colors.with_opacity(0.2, ft.Colors.GREY),
            width=1,
        ),
        left_axis=ft.ChartAxis(
            title=ft.Text("Taux de présence (%)", size=12),
            title_size=12,
            labels_size=10,
            labels_interval=20,  # Show labels every 20%
        ),
        bottom_axis=ft.ChartAxis(
            title=ft.Text("Classes", size=12),
            title_size=12,
        ),
        max_y=100,
        expand=True,
    )

    return ft.Container(
        content=chart,
        height=400,
        padding=ft.padding.all(40),
    )


def create_attendance_pie_chart(overall_stats):
    """
    Create a pie chart for overall attendance distribution.
    
    Args:
        overall_stats: Overall attendance statistics
        
    Returns:
        ft.PieChart: Pie chart component
    """
    total_present = overall_stats.get('total_present', 0)
    total_absent = overall_stats.get('total_absent', 0)
    
    if total_present == 0 and total_absent == 0:
        return ft.Container(
            content=ft.Text("Aucune donnée disponible", size=16, text_align=ft.TextAlign.CENTER),
            alignment=ft.alignment.center,
            height=300
        )
    
    sections = []
    total_all = total_present + total_absent

    if total_present > 0:
        present_percentage = round((total_present / total_all) * 100, 1) if total_all > 0 else 0
        sections.append(
            ft.PieChartSection(
                value=total_present,
                title=f"✅ Présent\n{total_present}\n({present_percentage}%)",
                title_style=ft.TextStyle(
                    size=12,
                    color=ft.Colors.WHITE,
                    weight=ft.FontWeight.BOLD,
                ),
                color=ft.Colors.GREEN_600,
                radius=100,
            )
        )

    if total_absent > 0:
        absent_percentage = round((total_absent / total_all) * 100, 1) if total_all > 0 else 0
        sections.append(
            ft.PieChartSection(
                value=total_absent,
                title=f"❌ Absent\n{total_absent}\n({absent_percentage}%)",
                title_style=ft.TextStyle(
                    size=12,
                    color=ft.Colors.WHITE,
                    weight=ft.FontWeight.BOLD,
                ),
                color=ft.Colors.RED_600,
                radius=100,
            )
        )
    
    chart = ft.PieChart(
        sections=sections,
        sections_space=2,
        center_space_radius=40,
        expand=True,
    )
    
    return ft.Container(
        content=chart,
        height=400,
        padding=ft.padding.all(40),
    )


def create_chart_container(title, chart_widget, subtitle=None):
    """
    Create a container for charts with title and styling.
    
    Args:
        title: Chart title
        chart_widget: The chart widget
        subtitle: Optional subtitle
        
    Returns:
        ft.Container: Styled chart container
    """
    content = [
        ft.Text(
            title,
            size=18,
            weight=ft.FontWeight.BOLD,
            text_align=ft.TextAlign.CENTER,
        )
    ]
    
    if subtitle:
        content.append(
            ft.Text(
                subtitle,
                size=12,
                color=ft.Colors.GREY_600,
                text_align=ft.TextAlign.CENTER,
            )
        )
    
    content.append(chart_widget)
    
    return ft.Container(
        content=ft.Column(
            content,
            alignment=ft.MainAxisAlignment.START,
            horizontal_alignment=ft.CrossAxisAlignment.CENTER,
            spacing=10,
        ),
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(12),
        padding=ft.padding.all(20),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=10,
            color=ft.Colors.with_opacity(0.1, ft.Colors.BLACK),
            offset=ft.Offset(0, 4)
        ),
        margin=ft.margin.all(10),
    )


def create_stats_summary_card(title, value, subtitle=None, icon=None, color=None):
    """
    Create a modern, simple summary statistics card.

    Args:
        title: Card title
        value: Main value to display
        subtitle: Optional subtitle
        icon: Optional icon
        color: Optional color theme

    Returns:
        ft.Container: Statistics card
    """
    color = color or ft.Colors.BLUE_600

    # Create icon with background circle
    icon_container = ft.Container(
        content=ft.Icon(icon, size=24, color=ft.Colors.WHITE),
        bgcolor=color,
        border_radius=ft.border_radius.all(25),
        width=50,
        height=50,
        alignment=ft.alignment.center,
    ) if icon else None

    # Main content layout
    content = []

    # Top row with icon and value
    top_row = []
    if icon_container:
        top_row.append(icon_container)

    # Handle None values properly
    display_value = value if value is not None else 0
    if isinstance(display_value, float):
        display_value = f"{display_value:.1f}" if display_value != int(display_value) else str(int(display_value))

    value_text = ft.Text(
        str(display_value),
        size=32,
        weight=ft.FontWeight.BOLD,
        color=ft.Colors.GREY_800,
    )
    top_row.append(value_text)

    if len(top_row) > 1:
        content.append(
            ft.Row(
                top_row,
                alignment=ft.MainAxisAlignment.SPACE_BETWEEN,
                vertical_alignment=ft.CrossAxisAlignment.CENTER,
            )
        )
    else:
        content.append(value_text)

    # Title
    content.append(
        ft.Text(
            title,
            size=16,
            weight=ft.FontWeight.W_500,
            color=ft.Colors.GREY_700,
        )
    )

    # Subtitle if provided
    if subtitle:
        content.append(
            ft.Text(
                subtitle,
                size=13,
                color=ft.Colors.GREY_500,
            )
        )

    return ft.Container(
        content=ft.Column(
            content,
            alignment=ft.MainAxisAlignment.START,
            horizontal_alignment=ft.CrossAxisAlignment.START,
            spacing=12,
        ),
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(16),
        padding=ft.padding.all(24),
        border=ft.border.all(1, ft.Colors.with_opacity(0.1, ft.Colors.GREY)),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=20,
            color=ft.Colors.with_opacity(0.08, ft.Colors.BLACK),
            offset=ft.Offset(0, 8)
        ),
        width=280,
        height=140,
    )


def create_percentage_card(title, percentage, total_count=None, icon=None, color=None):
    """
    Create a modern percentage display card.

    Args:
        title: Card title
        percentage: Percentage value
        total_count: Optional total count to display
        icon: Optional icon
        color: Optional color theme

    Returns:
        ft.Container: Percentage card
    """
    color = color or ft.Colors.BLUE_600

    # Handle None values properly
    display_percentage = percentage if percentage is not None else 0
    if isinstance(display_percentage, float):
        display_percentage = round(display_percentage, 1)

    # Create circular progress indicator
    progress_circle = ft.Container(
        content=ft.Stack([
            ft.Container(
                width=60,
                height=60,
                border_radius=ft.border_radius.all(30),
                bgcolor=ft.Colors.with_opacity(0.1, color),
            ),
            ft.Container(
                content=ft.Text(
                    f"{display_percentage}%",
                    size=14,
                    weight=ft.FontWeight.BOLD,
                    color=color,
                ),
                width=60,
                height=60,
                alignment=ft.alignment.center,
            ),
        ]),
        width=60,
        height=60,
    )

    # Content layout
    content = [
        ft.Row([
            progress_circle,
            ft.Column([
                ft.Text(
                    title,
                    size=16,
                    weight=ft.FontWeight.W_500,
                    color=ft.Colors.GREY_700,
                ),
                ft.Text(
                    f"{total_count} total" if total_count else "",
                    size=12,
                    color=ft.Colors.GREY_500,
                ) if total_count else ft.Container(),
            ], spacing=4, expand=True)
        ], alignment=ft.MainAxisAlignment.START, spacing=16)
    ]

    return ft.Container(
        content=ft.Column(
            content,
            alignment=ft.MainAxisAlignment.CENTER,
            spacing=8,
        ),
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(16),
        padding=ft.padding.all(20),
        border=ft.border.all(1, ft.Colors.with_opacity(0.1, ft.Colors.GREY)),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=20,
            color=ft.Colors.with_opacity(0.08, ft.Colors.BLACK),
            offset=ft.Offset(0, 8)
        ),
        width=280,
        height=100,
    )


def create_compact_stat_card(title, value, change=None, icon=None, color=None):
    """
    Create a compact statistics card for smaller displays.

    Args:
        title: Card title
        value: Main value to display
        change: Optional change indicator (e.g., "+5%")
        icon: Optional icon
        color: Optional color theme

    Returns:
        ft.Container: Compact statistics card
    """
    color = color or ft.Colors.BLUE_600

    content = [
        ft.Row([
            ft.Icon(icon, size=20, color=color) if icon else ft.Container(),
            ft.Text(
                title,
                size=14,
                weight=ft.FontWeight.W_500,
                color=ft.Colors.GREY_600,
                expand=True,
            ),
        ], spacing=8),
        ft.Row([
            ft.Text(
                str(value if value is not None else 0),
                size=24,
                weight=ft.FontWeight.BOLD,
                color=ft.Colors.GREY_800,
            ),
            ft.Text(
                change or "",
                size=12,
                weight=ft.FontWeight.W_500,
                color=ft.Colors.GREEN_600 if change and change.startswith('+') else ft.Colors.RED_600,
            ) if change else ft.Container(),
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
    ]

    return ft.Container(
        content=ft.Column(
            content,
            spacing=8,
        ),
        bgcolor=ft.Colors.WHITE,
        border_radius=ft.border_radius.all(12),
        padding=ft.padding.all(16),
        border=ft.border.all(1, ft.Colors.with_opacity(0.1, ft.Colors.GREY)),
        shadow=ft.BoxShadow(
            spread_radius=0,
            blur_radius=15,
            color=ft.Colors.with_opacity(0.06, ft.Colors.BLACK),
            offset=ft.Offset(0, 4)
        ),
        width=200,
        height=80,
    )


def create_quiz_score_line_chart(daily_stats):
    """
    Create a line chart for daily quiz scores.

    Args:
        daily_stats: List of daily quiz statistics

    Returns:
        ft.LineChart: Line chart component
    """
    if not daily_stats:
        return ft.Container(
            content=ft.Text("Aucune donnée disponible", size=16, text_align=ft.TextAlign.CENTER),
            alignment=ft.alignment.center,
            height=300
        )

    # Prepare data points with tooltips
    avg_data_points = []
    max_data_points = []
    min_data_points = []

    for i, stat in enumerate(daily_stats):
        date_str = stat.get('date', '')
        formatted_date = date_str
        if date_str:
            try:
                from datetime import datetime
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                formatted_date = date_obj.strftime('%d/%m')
            except:
                formatted_date = date_str[-5:]

        avg_data_points.append(
            ft.LineChartDataPoint(
                x=i,
                y=stat.get('avg_score', 0),
                tooltip=f"Date: {formatted_date}\nScore moyen: {stat.get('avg_score', 0)}%"
            )
        )
        max_data_points.append(
            ft.LineChartDataPoint(
                x=i,
                y=stat.get('max_score', 0),
                tooltip=f"Date: {formatted_date}\nMeilleur score: {stat.get('max_score', 0)}%"
            )
        )
        min_data_points.append(
            ft.LineChartDataPoint(
                x=i,
                y=stat.get('min_score', 0),
                tooltip=f"Date: {formatted_date}\nScore minimum: {stat.get('min_score', 0)}%"
            )
        )

    # Create legend
    legend = ft.Row([
        ft.Row([
            ft.Container(width=20, height=3, bgcolor=ft.Colors.BLUE_600),
            ft.Text("Score moyen", size=10, color=ft.Colors.GREY_700),
        ], spacing=5),
        ft.Row([
            ft.Container(width=20, height=3, bgcolor=ft.Colors.GREEN_600),
            ft.Text("Meilleur score", size=10, color=ft.Colors.GREY_700),
        ], spacing=5),
        ft.Row([
            ft.Container(width=20, height=3, bgcolor=ft.Colors.RED_600),
            ft.Text("Score minimum", size=10, color=ft.Colors.GREY_700),
        ], spacing=5),
    ], spacing=15, alignment=ft.MainAxisAlignment.CENTER)

    # Create chart
    chart = ft.LineChart(
        data_series=[
            ft.LineChartData(
                data_points=avg_data_points,
                stroke_width=3,
                color=ft.Colors.BLUE_600,
                curved=True,
                stroke_cap_round=True,
            ),
            ft.LineChartData(
                data_points=max_data_points,
                stroke_width=2,
                color=ft.Colors.GREEN_600,
                curved=True,
                stroke_cap_round=True,
            ),
            ft.LineChartData(
                data_points=min_data_points,
                stroke_width=2,
                color=ft.Colors.RED_600,
                curved=True,
                stroke_cap_round=True,
            ),
        ],
        border=ft.Border(
            bottom=ft.BorderSide(2, ft.Colors.with_opacity(0.8, ft.Colors.GREY)),
            left=ft.BorderSide(2, ft.Colors.with_opacity(0.8, ft.Colors.GREY)),
        ),
        horizontal_grid_lines=ft.ChartGridLines(
            color=ft.Colors.with_opacity(0.2, ft.Colors.GREY),
            width=1,
        ),
        vertical_grid_lines=ft.ChartGridLines(
            color=ft.Colors.with_opacity(0.2, ft.Colors.GREY),
            width=1,
        ),
        left_axis=ft.ChartAxis(
            title=ft.Text("Score (%)", size=12),
            title_size=12,
            labels_size=10,
            labels_interval=20,  # Show labels every 20%
        ),
        bottom_axis=ft.ChartAxis(
            title=ft.Text("Dates", size=12),
            title_size=12,
            labels_size=10,
        ),
        min_y=0,
        max_y=100,
        expand=True,
    )

    return ft.Container(
        content=ft.Column([
            legend,
            chart,
        ], spacing=10),
        height=450,
        padding=ft.padding.all(40),
    )


def create_quiz_performance_bar_chart(class_stats):
    """
    Create a bar chart for quiz performance by class.

    Args:
        class_stats: List of class quiz statistics

    Returns:
        ft.BarChart: Bar chart component
    """
    if not class_stats:
        return ft.Container(
            content=ft.Text("Aucune donnée disponible", size=16, text_align=ft.TextAlign.CENTER),
            alignment=ft.alignment.center,
            height=300
        )

    # Prepare bar groups with class names
    bar_groups = []
    for i, stat in enumerate(class_stats[:8]):  # Limit to 8 classes for better readability
        class_name = stat.get('class_name', f'Classe {i+1}')

        bar_groups.append(
            ft.BarChartGroup(
                x=i,
                bar_rods=[
                    ft.BarChartRod(
                        from_y=0,
                        to_y=stat.get('avg_score', 0),
                        width=25,
                        color=ft.Colors.PURPLE_600,
                        tooltip=f"{class_name}: {stat.get('avg_score', 0)}%\nSoumissions: {stat.get('total_submissions', 0)}",
                        border_radius=ft.border_radius.only(top_left=4, top_right=4),
                        tooltip_style=ft.TextStyle(
                            color=ft.Colors.WHITE,
                            size=12,
                            weight=ft.FontWeight.BOLD,
                        ),
                    )
                ],
            )
        )

    chart = ft.BarChart(
        bar_groups=bar_groups,
        border=ft.Border(
            bottom=ft.BorderSide(2, ft.Colors.with_opacity(0.8, ft.Colors.GREY)),
            left=ft.BorderSide(2, ft.Colors.with_opacity(0.8, ft.Colors.GREY)),
        ),
        horizontal_grid_lines=ft.ChartGridLines(
            color=ft.Colors.with_opacity(0.2, ft.Colors.GREY),
            width=1,
        ),
        left_axis=ft.ChartAxis(
            title=ft.Text("Score moyen (%)", size=12),
            title_size=12,
            labels_size=10,
            labels_interval=20,  # Show labels every 20%
        ),
        bottom_axis=ft.ChartAxis(
            title=ft.Text("Classes", size=12),
            title_size=12,
        ),
        max_y=100,
        expand=True,
    )

    return ft.Container(
        content=chart,
        height=400,
        padding=ft.padding.all(40),
    )


def create_quiz_score_distribution_pie_chart(score_distribution):
    """
    Create a pie chart for quiz score distribution.

    Args:
        score_distribution: List of score distribution data

    Returns:
        ft.PieChart: Pie chart component
    """
    if not score_distribution:
        return ft.Container(
            content=ft.Text("Aucune donnée disponible", size=16, text_align=ft.TextAlign.CENTER),
            alignment=ft.alignment.center,
            height=300
        )

    # Color mapping for score ranges
    colors = {
        '90-100%': ft.Colors.GREEN_600,
        '80-89%': ft.Colors.LIGHT_GREEN_600,
        '70-79%': ft.Colors.YELLOW_600,
        '60-69%': ft.Colors.ORANGE_600,
        'Below 60%': ft.Colors.RED_600,
    }

    # Calculate total for percentages
    total_submissions = sum(dist.get('count', 0) for dist in score_distribution)

    sections = []
    for dist in score_distribution:
        score_range = dist.get('score_range', '')
        count = dist.get('count', 0)

        if count > 0:
            percentage = round((count / total_submissions) * 100, 1) if total_submissions > 0 else 0

            # Add emoji for better visual distinction
            emoji_map = {
                '90-100%': '🏆',
                '80-89%': '🥇',
                '70-79%': '🥈',
                '60-69%': '🥉',
                'Below 60%': '📉'
            }
            emoji = emoji_map.get(score_range, '📊')

            sections.append(
                ft.PieChartSection(
                    value=count,
                    title=f"{emoji} {score_range}\n{count} ({percentage}%)",
                    title_style=ft.TextStyle(
                        size=10,
                        color=ft.Colors.WHITE,
                        weight=ft.FontWeight.BOLD,
                    ),
                    color=colors.get(score_range, ft.Colors.GREY_600),
                    radius=100,
                )
            )

    chart = ft.PieChart(
        sections=sections,
        sections_space=2,
        center_space_radius=40,
        expand=True,
    )

    return ft.Container(
        content=chart,
        height=400,
        padding=ft.padding.all(40),
    )
